import { useState, useMemo } from "react";
import { useAuth } from "~/lib/auth-context";
import { useTheme } from "~/lib/theme-context";
import { But<PERSON> } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { ExpenseChart } from "~/components/expense-chart";
import { ExpenseTable } from "~/components/expense-table";
import {
  mockExpenses,
  type TimePeriod,
  type ViewMode,
  type ExpenseCategory,
  EXPENSE_CATEGORIES,
} from "~/lib/types";
import {
  filterExpensesByPeriod,
  filterExpensesByCategory,
  getTotalExpenses,
  formatCurrency,
} from "~/lib/expense-utils";
import { LogOut, Sun, Moon, BarChart3, Table } from "lucide-react";

export function Dashboard() {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("monthly");
  const [selectedCategory, setSelectedCategory] = useState<
    ExpenseCategory | "all"
  >("all");
  const [viewMode, setViewMode] = useState<ViewMode>("chart");

  const filteredExpenses = useMemo(() => {
    let expenses = filterExpensesByPeriod(mockExpenses, timePeriod);
    expenses = filterExpensesByCategory(expenses, selectedCategory);
    return expenses;
  }, [timePeriod, selectedCategory]);

  const totalExpenses = useMemo(
    () => getTotalExpenses(filteredExpenses),
    [filteredExpenses],
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Expense Tracker</h1>
            <p className="text-muted-foreground">Welcome back, {user?.name}</p>
          </div>

          <div className="flex items-center gap-4">
            {/* Theme Toggle */}
            <div className="flex items-center gap-2">
              <Sun className="h-4 w-4" />
              <Switch
                checked={theme === "dark"}
                onCheckedChange={toggleTheme}
              />
              <Moon className="h-4 w-4" />
            </div>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  {user?.name}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={logout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Controls */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-wrap gap-4">
            {/* Time Period Selector */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Period:</label>
              <Select
                value={timePeriod}
                onValueChange={(value: TimePeriod) => setTimePeriod(value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Category Selector */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Category:</label>
              <Select
                value={selectedCategory}
                onValueChange={(value: ExpenseCategory | "all") =>
                  setSelectedCategory(value)
                }
              >
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {EXPENSE_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "chart" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("chart")}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Chart
            </Button>
            <Button
              variant={viewMode === "table" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("table")}
            >
              <Table className="mr-2 h-4 w-4" />
              Table
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Total Expenses
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(totalExpenses)}
              </div>
              <p className="text-xs text-muted-foreground">
                {filteredExpenses.length} transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Average per Transaction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(
                  filteredExpenses.length > 0
                    ? totalExpenses / filteredExpenses.length
                    : 0,
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Based on {filteredExpenses.length} transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Time Period</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold capitalize">{timePeriod}</div>
              <p className="text-xs text-muted-foreground">
                {selectedCategory === "all"
                  ? "All categories"
                  : selectedCategory}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <CardTitle>
              Expense {viewMode === "chart" ? "Chart" : "Table"}
            </CardTitle>
            <CardDescription>
              {viewMode === "chart"
                ? "Visual representation of your expenses over time"
                : "Detailed list of all your expenses"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {viewMode === "chart" ? (
              <ExpenseChart
                expenses={filteredExpenses}
                timePeriod={timePeriod}
                category={selectedCategory}
              />
            ) : (
              <ExpenseTable expenses={filteredExpenses} />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
