export interface User {
  id: string;
  email: string;
  name: string;
}

export interface Expense {
  id: string;
  amount: number;
  category: string;
  description: string;
  date: string;
  merchant?: string;
}

export type TimePeriod = "daily" | "weekly" | "monthly";
export type ViewMode = "chart" | "table";

export const EXPENSE_CATEGORIES = [
  "Food & Dining",
  "Transportation",
  "Shopping",
  "Entertainment",
  "Bills & Utilities",
  "Healthcare",
  "Travel",
  "Education",
  "Other",
] as const;

export type ExpenseCategory = (typeof EXPENSE_CATEGORIES)[number];

// Mock data for demonstration
export const mockExpenses: Expense[] = [
  {
    id: "1",
    amount: 25.5,
    category: "Food & Dining",
    description: "Lunch at restaurant",
    date: "2024-01-15",
    merchant: "Restaurant ABC",
  },
  {
    id: "2",
    amount: 45.0,
    category: "Transportation",
    description: "Gas station",
    date: "2024-01-14",
    merchant: "Shell Gas",
  },
  {
    id: "3",
    amount: 120.0,
    category: "Shopping",
    description: "Grocery shopping",
    date: "2024-01-13",
    merchant: "Walmart",
  },
  {
    id: "4",
    amount: 15.99,
    category: "Entertainment",
    description: "Movie ticket",
    date: "2024-01-12",
    merchant: "Cinema Plus",
  },
  {
    id: "5",
    amount: 85.0,
    category: "Bills & Utilities",
    description: "Electric bill",
    date: "2024-01-11",
    merchant: "Electric Company",
  },
  {
    id: "6",
    amount: 32.75,
    category: "Food & Dining",
    description: "Coffee and breakfast",
    date: "2024-01-10",
    merchant: "Starbucks",
  },
  {
    id: "7",
    amount: 200.0,
    category: "Healthcare",
    description: "Doctor visit",
    date: "2024-01-09",
    merchant: "Medical Center",
  },
  {
    id: "8",
    amount: 67.5,
    category: "Transportation",
    description: "Uber ride",
    date: "2024-01-08",
    merchant: "Uber",
  },
  {
    id: "9",
    amount: 89.99,
    category: "Shopping",
    description: "Online purchase",
    date: "2024-01-07",
    merchant: "Amazon",
  },
  {
    id: "10",
    amount: 42.3,
    category: "Food & Dining",
    description: "Dinner with friends",
    date: "2024-01-06",
    merchant: "Italian Bistro",
  },
];
